﻿<!DOCTYPE html>
<html>

<head>
    <title>AI Posting Job Salary</title>
    <link rel="stylesheet" type="text/css" href="styles.css">
    <link rel="stylesheet" href="bootstrap-salary-web.min.css">
</head>

<body>
   <!--  <div class="icon_floatingLogo hide">
        <button class="button-blue"  id="floatingLogo">
            <img src="icon_floating logo.svg" class="" alt="">
        </button>
    </div> -->
    <div class="icon_sa-expandArrow sa-expandArrow-border" id="icon-arrow">
        <div style="margin-bottom: 10px;">
            <button class="button-blue" onclick="expandModal()">
                <img src="sa-expandArrow.svg " alt="" style="height: 15px;">
            </button>
        </div>
        <div>
            <button class="button-blue" onclick="closeWindow()">
                <img src="sa-remove.svg" alt="">
            </button>
        </div>
    </div>
    <div class="sa-plugs-container" id="panel">
        <div class="salaryPicture">
            <img class="sa-logo" src="https://www.salary.com/wwwroot/img/header/Header_SalaryLogo.svg" alt="Salary.com">
            <span>Real-time Job Posting Salary Data</span>
        </div>
        <div class="sa-job-info" id="first-modal" style="display: block;">
         
            <div class="inputSection margin-botton30px">
                <p class="sa-paragraph">Seamlessly integrated with major ATS and job boards. Access job info, estimated salaries, and real-time job data with a single click.</p>
                <button id="sa-getsalryforthisjob-btn" class="button-color margin-botton30px" style="margin-left: 10px;" onclick="GetSalaryForThisJob();">
                    <spana style="font-weight: bold;">Get Salary Range For This Job</span>
                </button>
                <div style="text-align: left; margin-left: 10px;">
                    <div class="text-charcoal" style="margin-bottom: 10px; font-size: 16px; font-weight: 600;">Get Custom Salary Estimate</div>
                    <div style="display: flex; margin-bottom: 10px;">
                        <div class="text-charcoal text-size12px">Job Title</div>
                        <span class="font-semibold text-size12px cursor-pointer" style="color: #007dbc; margin-left: auto; margin-right: 10px;" onclick="autoFillDesc();">Populate Job
                            Description</span>
                    </div>
                    
                    <input type='text' class="form-control modal-input" id="JobTitle"/><br />
                    <span class="text-charcoal text-size12px">Job description</span>
                   
                    <textarea class="form-control modal-input" id="JobDesc" rows="10" cols="50"
                        style="margin-top: 10px;"></textarea><br />
                    <div class="margin-bottom15px ">
                        <span class="font-weight600 text-size14px text-black" style="margin-right: 5px;">Scope Data</span>
                        <a class="text-lightblue cursor-pointer" style="font-size: 13px; text-decoration: underline;" onclick="RedictSalarySite();">Want to adjust scopes? Please contact us.</a>
                    </div>
                    <div class="display-flex">
                        <div class="margin-bottom15px text-size15px display-inline text-black flex-basis-60">Industry</div>
                        <select name="" id="" class="select-option"  disabled>
                            <option value="">All</option>
                        </select>
                    </div>
                    <div class="display-flex">
                        <div class="margin-bottom15px text-size15px display-inline text-black flex-basis-60">City</div>
                        <select name="" id="" class="select-option " disabled>
                            <option value="">All</option>
                        </select>
                    </div>
                    <div class="display-flex">
                        <div class="margin-bottom15px text-size15px flex-basis-60">Company Size</div>
                        <select name="" id="" class="select-option" disabled>
                            <option value="">All</option>
                        </select>
                    </div>
                    <button class="button-color margin-botton30px" id='GetJobSalary'>
                        <spana style="font-weight: bold;">Recommend Salary Range</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="sa-salary-info" id="second-modal" style="display: none;">
            <div class="section-recommend">
                <div class="back-position text-size12px">
                    <button class="button-blue" onclick="back()"><img src="icons/sa-triangle-left.svg" alt=""
                            class="icon-triangle-left"><span>Back</span></button>
                </div>
                <div class="match-rating">
                    <span class="text-size12px text-charcoal" style="margin-left: 10px">MATCH RATING</span>
                    <div class="flex-div flex-justify-content-between flex-align-item-center" style="margin-left: 70px;">
                        <img src="icons/green dot.svg" alt=""
                        class="icon-green-dot" style="margin-right: 10px; margin-top: -2px;"><span class="confident-match text-size14px" style="color:#000000;">
                        Confident Match</span>
                    </div>
                  
                </div>
                <div class="sa-content">
                    <div class="sa-salary-div margin-left10px">
                        <div class="recommend">
                            <span class="text-black">Recommend Salary Range</span>
                        </div>
                        <div class="line margin-bottom15px"></div>
                        <div class="text-lightblue compensation">
                            <button class="button-blue" data-toggle="collapse" data-target="#sa-compensation-data">
                                <img src="icons/sa-triangle-down.svg" alt=""
                                    class="icon-triangle-down">
                                    <img src="icons/sa-triangle-right.svg" alt=""
                                    class="icon-triangle-right">
                                    <span>Estimated Salary in USA</span>
                                </button>
                        </div>
                        <div class="salary margin-bottom15 collapse in" id="sa-compensation-data" aria-expanded="true">
                            <div class="margin-left10px text-size12px">
                                <div class="margin-bottom15px" style="border-bottom: 1px dashed #ffffff;">
                                    <span class="text-charcoal">Salary 25th</span> <span class="margin-left70px text-size15px sa-salary25"></span>
                                </div>
                                <div class="margin-bottom15px" style="border-bottom: 1px dashed #ffffff;">
                                    <span class="text-charcoal">Salary 50th</span> <span class="margin-left70px text-size15px sa-salary50"></span>
                                </div>
                                <div class="margin-bottom15px" style="border-bottom: 1px dashed #ffffff;">
                                    <span class="text-charcoal">Salary 75th</span> <span class="margin-left70px text-size15px sa-salary75"></span>
                                </div>
                            </div>
                        </div>
                        <div class="text-size12px">
                            <button class="button-blue" data-toggle="collapse" data-target="#sa-jobposting-data">
                                <img src="icons/sa-triangle-down.svg" alt=""
                                    class="icon-triangle-down">
                                    <img src="icons/sa-triangle-right.svg" alt=""
                                    class="icon-triangle-right">
                                    <span>Job Posting Data in USA</span></button>
                        </div>
                        <div class="collapse in" id="sa-jobposting-data" aria-expanded="true">
                            <div class="margin-left10px text-size12px margin-top15">
                                <div class="">
                                    <p class="text-charcoal display-inline" style="width: 90px;">Job Posting Median</p>
                                    <p class="display-inline margin-left70px text-size15px filter-blur blur-text">$101, 128</p>
                                </div>
                                <div class="">
                                    <p class="text-charcoal display-inline margin-top0" style="width: 90px;"># Of Postings</p>
                                    <p class="display-inline margin-left70px text-size15px filter-blur blur-text">$101, 128</p>
                                </div>
                                <div class="">
                                    <p class="text-charcoal display-inline margin-top0" style="width: 90px;"># Of Hiring Companies
                                    </p>
                                    <p class="display-inline margin-left70px text-size15px filter-blur blur-text">$101, 128</p>
                                </div>
                                <div class="">
                                    <p class="text-charcoal display-inline margin-top0" style="width: 90px;">Avg Posting Duration
                                    </p>
                                    <p class="display-inline margin-left70px text-size15px filter-blur blur-text">$101, 128</p>
                                </div>
                                <p class="text-charcoal display-inline margin-top0" style="width: 90px;">More...</p>
                                <div class="text-lightblue margin-bottom15px cursor-pointer" onclick="RedictSalarySite()" style="font-size: 12px;">
                                    Please contact us for full access to job posting data
                                </div>
                            </div>
                        </div>
                       
                        <div class="line margin-bottom15px"></div>
                        <div class="margin-bottom15px">
                            <button class="button-blue sa-scope-btn collapsed"  data-toggle="collapse" data-target="#sa-trending-data" aria-expanded="false">
                                <img  src="icons/sa-triangle-down.svg" alt=""
                                class="icon-triangle-down">
                                <img src="icons/sa-triangle-right.svg" alt=""
                                class="icon-triangle-right">
                                <span class="text-size12px">Job Salary Trending</span>
                            </button>
                            <div class="collapse" id="sa-trending-data" aria-expanded="true">
                                <div class="margin-left10px text-size12px margin-top15">
                                    <div class="">
                                        <p class="text-charcoal display-inline" style="width: 90px;">2024-07</p>
                                        <p class="display-inline margin-left70px text-size15px  blur-text">$<span class="filter-blur">101, 128</span>-$<span class="filter-blur">101, 128</span></p>
                                    </div>
                                    <div class="">
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">2024-08</p>
                                        <p class="display-inline margin-left70px text-size15px  blur-text">$<span class="filter-blur">101, 128</span>-$<span class="filter-blur">101, 128</span></p>
                                    </div>
                                    <div class="">
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">2024-09
                                        </p>
                                        <p class="display-inline margin-left70px text-size15px  blur-text">$<span class="filter-blur">101, 128</span>-$<span class="filter-blur">101, 128</span></p>
                                    </div>
                                    <div class="">
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">2024-10
                                        </p>
                                        <p class="display-inline margin-left70px text-size15px blur-text">$<span class="filter-blur">101, 128</span>-$<span class="filter-blur">101, 128</span></p>
                                    </div>      
                                    <p class="text-charcoal display-inline margin-top0" style="width: 90px;">More...</p>                         
                                    <div class="text-lightblue margin-bottom15px cursor-pointer" onclick="RedictSalarySite()" style="font-size: 12px;">
                                        Please contact us for full access to job salary trending data
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="margin-bottom15px">
                            <button class="button-blue sa-scope-btn collapsed"  data-toggle="collapse" data-target="#sa-scope-data" aria-expanded="false">
                                <img  src="icons/sa-triangle-down.svg" alt=""
                                class="icon-triangle-down">
                                <img src="icons/sa-triangle-right.svg" alt=""
                                class="icon-triangle-right">
                                <span class="text-size12px">Comprehensive Peer Analysis</span>
                            </button>
                            <div class="collapse" id="sa-scope-data" aria-expanded="true">
                                <div class="margin-left10px text-size12px margin-top15">
                                    <div class="">
                                        <p class="text-charcoal display-inline" style="width: 90px;">Industry Name</p>
                                        <p class="display-inline margin-left70px text-size15px filter-blur blur-text">Industry Name</p>
                                    </div>
                                    <div class="">
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">FTE Range</p>
                                        <p class="display-inline margin-left70px text-size15px filter-blur blur-text">FTE Range</p>
                                    </div>
                                    <div class="">
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">Country Code
                                        </p>
                                        <p class="display-inline margin-left70px text-size15px filter-blur blur-text">Country Code</p>
                                    </div>
                                    <div class="">
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">Region
                                        </p>
                                        <p class="display-inline margin-left70px text-size15px filter-blur blur-text">Region</p>
                                    </div>
                                    <div class="">
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">State
                                        </p>
                                        <p class="display-inline margin-left70px text-size15px filter-blur blur-text">State</p>
                                    </div>
                                    <div class="">
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">City
                                        </p>
                                        <p class="display-inline margin-left70px text-size15px filter-blur blur-text">City</p>
                                    </div>
                                    <p class="text-charcoal display-inline margin-top0" style="width: 90px;">More...</p>
                                    <div class="text-lightblue margin-bottom15px cursor-pointer" onclick="RedictSalarySite()" style="font-size: 12px;">
                                        Please contact us for full access to peer analysis data
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="margin-bottom15px">
                            <button class="button-blue sa-job-btn collapsed" data-toggle="collapse" data-target="#sa-job-data" aria-expanded="false">
                                <img src="icons/sa-triangle-down.svg" alt=""
                                class="icon-triangle-down">
                                <img src="icons/sa-triangle-right.svg" alt=""
                                class="icon-triangle-right">
                                <span class="text-size12px">Job Data</span></button>
                                <div class="collapse" id="sa-job-data" aria-expanded="true">
                                    <div class="margin-left10px text-size12px margin-top15">
                                        <div class="">
                                            <p class="text-charcoal display-inline" style="width: 90px;">Job Level</p>
                                            <p class="display-inline margin-left70px text-size15px filter-blur blur-text">Job Level</p>
                                        </div>
                                        <div class="">
                                            <p class="text-charcoal display-inline margin-top0" style="width: 90px;">Experience Year Min / Max                                           </p>
                                            <p class="display-inline margin-left70px text-size15px filter-blur blur-text">Experience Year Min / Max</p>
                                        </div>
                                        <div class="">
                                            <p class="text-charcoal display-inline margin-top0" style="width: 90px;">Degree
                                            </p>
                                            <p class="display-inline margin-left70px text-size15px filter-blur blur-text">Degree</p>
                                        </div>
                                        <div class="">
                                            <p class="text-charcoal display-inline margin-top0" style="width: 90px;">Skills
                                            </p>
                                            <p class="display-inline margin-left70px text-size15px filter-blur blur-text">Skills</p>
                                        </div>
                                        <p class="text-charcoal display-inline margin-top0" style="width: 90px;">More...</p>
                                        <div class="text-lightblue margin-bottom15px cursor-pointer" onclick="RedictSalarySite()" style="font-size: 12px;">
                                            Please contact us for full access to job data
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </div>
                    <div class="contact-section">
                        <div>
                            <p class="paragraph">For more information on compensation data or to explore additional
                                sections, feel free to get in touch with us.</p>
                        </div>
                        <div class="button-contact">
                            <a onclick="RedictSalarySite()" ><button class="button-color2">
                                <span style="font-weight: bold; color: #FFF;">Contact Us</span>
                            </button></a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="sa-nodata-div" style="display: none;">
            <div class="section-recommend">
                <div class="back-position text-size12px">
                    <button class="button-blue" onclick="back()"><img src="icons/sa-triangle-left.svg" alt=""
                            class="icon-triangle-left"><span>Back</span></button>
                </div>
                <div class="match-rating">
                    <span class="text-size12px text-charcoal" style="margin-left: 10px">MATCH RATING</span>
                    <div class="flex-div flex-justify-content-between flex-align-item-center" style="margin-left: 70px;">
                        <img src="icons/green dot.svg" alt=""
                        class="icon-green-dot" style="margin-right: 10px; margin-top: -2px;"><span class="confident-match text-size14px" style="color:#000000;">
                        Confident Match</span>
                    </div>
                  
                </div>
                <div class="bg-white">
                    <div class="margin-left10px">
                        <div class="recommend">
                            <span class="text-black">Recommend Salary Range</span>
                        </div>
                        <div class="line margin-bottom15px"></div>
                        <div class="text-align-left display-flex flex-align-item-center">
                            <img class="margin-right10" src="sa-find.svg" style="width:18.88px; height:15.71px;"/><div class="text-black text-size15px margin-top10">We do not have salary data for this job.</p>
                        </div>
                    </div>

                </div>
            </div>
            
        </div>
 
        <script src="jquery.min.js"></script>
        <script src="main.js"></script>
        <script src="bootstrap-salary-web.js"></script>    
    </div>
    <div id="sal-ctalinkform-container">

        <div class="modal fade sal-sharelink-popup" tabindex="-1" data-keyboard="false" data-backdrop="static" role="dialog" id="sal_sharelink_popup">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header margin-top20 padding-bottom20">
                        <div class="flex-div flex-align-item-start flex-justify-content-between flex-nowrap">
                            <div class="flex-div flex-align-item-start">
                                <img src="sa-share.png" class="margin-right20" />
                                <span class="font-blod" style="color: #212221;; font-size: 15px;">Share this salary tool with your friends.</span>
                            </div>
                            <img class="cursor-pointer" src="sa-remove.svg" data-dismiss="modal" style="position:absolute; top:0; right:0;" />
                        </div>
                    </div>
                    <div class="modal-body padding-top0">
                        <div class="sal-form-container">
   
                            <div class="border-none sal-demo-field"  style="margin-right:0;">
                                <div class="sa-inputcontent-wrapper margin-top10">
                                    <div class="flex-div flex-basis-fill sal-demo-required">
                                        <div class="input-div">
                                            <input id="select_data" type="text" maxlength="120" class="modal-input" value="" />
                                            <span class="icon-remove collapse" onclick="javascript: clearInput(this);"></span>
                                            <button class="margin-left20 margin-bottom5 btn sa-demo-btn sal-btn-apply" onclick="javascript: copyToClipboard(this);"><span>Copy</span></button>
   
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer padding-top0">
                     </div>
                </div>
            </div>
        </div>
    </div>  
</div>
<div class="footer">
    <p>© Copyright Salary.com 2024. All Rights Reserved.</p>
</div>
</body>
<script src="https://assets.adobedtm.com/b590647a1ee3/72493d5bb63a/launch-cf399e951d71.min.js" async></script>
<script>
     var salTrackingData = window.salTrackingData || {};
     salTrackingData.page = {};
     salTrackingData.page.category = {};
     salTrackingData.page.pageInfo = {};
     salTrackingData.page.category.primaryCategory = 'plugin';
     salTrackingData.page.pageInfo.pageName = 'plugin: chormplugin';
</script>
</html>