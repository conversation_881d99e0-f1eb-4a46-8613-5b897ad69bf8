var retried = false;
var arrATSSiteURL=[];
var jobSalaryHost= "http://localhost:8889"; // Local development server
var localApiVersion=localStorage.getItem("apiversion");  
var jdspiderData=localStorage.getItem("jdspiderData");

function initPlugins(){
var collapseImg=document.getElementById('sa-collaspse-icon');
if(collapseImg){
    document.body.removeChild(collapseImg);
}
collapseImg=document.createElement('img');
collapseImg.id="sa-collaspse-icon";
collapseImg.src =jobSalaryHost+"/ChromePlugin/img/icon_floating logo.svg" //jobSalaryHost+"/JobSalary/Index.html?v=2";
collapseImg.alt ="collaspse img";
collapseImg.style.top = '20%';
collapseImg.style.right = 0;
collapseImg.style.position = 'fixed';
collapseImg.style.width = '54px';
collapseImg.style.height = '54px';
collapseImg.style.cursor = 'pointer';
collapseImg.style.zIndex="999999";
document.body.appendChild(collapseImg);
    //let arrATSSiteUrl=localStorage.getItem("arrATSSiteUrl");
    fetch(jobSalaryHost+'/JobSalary/jdspiderversion.txt').then(reponse=>{
        return reponse;
    }).then(response=>response.text()).then(data=>{
            if(localApiVersion==null){
                localStorage.setItem("apiVersion", data);
                GetJDSpiderList();
            }else{
                if(serverApiVersion!=localApiVersion){
                    localStorage.setItem("apiVersion", data);
                    GetJDSpiderList();
                }
            }
    });
}
function initFrame() { 
    // If the iframe already exists, return early
    var myIframe = document.getElementById('postingJobSalaryIframe');
    if (myIframe) {
        document.body.removeChild(myIframe);
    }
    myIframe = document.createElement('iframe');
    //jobSalaryHost ="https://salarystaging61.salarynet.local:8889/" //"https://ai-demo.salarywizard.cn:8889";
    // Set the iframe attributes
    myIframe.id = 'postingJobSalaryIframe';
    myIframe.src = jobSalaryHost+"/JobSalary/Index.html";//"http://localhost:8889/JobSalary/index.html";//jobSalaryHost+"/JobSalary/Index.html?v=2";
    myIframe.style.top = 0;
    myIframe.style.right = 0;
    myIframe.style.position = 'fixed';
    myIframe.style.width = '375px';
    myIframe.style.zIndex = 9999;
    myIframe.style.height = '100%'; // You can adjust the height as needed
    myIframe.style.boxShadow = '0 0 30px 0 rgba(0, 0, 0, 0.5)';
    myIframe.style.border = '1px';
    myIframe.style.backgroundColor = 'white';
    myIframe.style.direction = 'ltr';
    // Append the iframe to the body
    document.body.appendChild(myIframe);
    var collapseImg=document.getElementById('sa-collaspse-icon');
    collapseImg.style.display="none";
   // setTimeout(extractJobPostingInfoAndSendMessage, 500);
}

function initViewSalaryButton() { 
    // If the iframe already exists, return early
    var btnViewSalaryButton = document.getElementById('viewSalaryButton');
    if (btnViewSalaryButton) {
        document.body.removeChild(btnViewSalaryButton);
    }
    btnViewSalaryButton = document.createElement('button');
    //jobSalaryHost ="https://salarystaging61.salarynet.local:8889/" //"https://ai-demo.salarywizard.cn:8889";
    // Set the iframe attributes
    btnViewSalaryButton.id = 'viewSalaryButton';
    btnViewSalaryButton.src = jobSalaryHost+"/JobSalary/Index.html";//"http://localhost:8889/JobSalary/index.html";//jobSalaryHost+"/JobSalary/Index.html?v=2";
    myIframe.style.top = 0;
    myIframe.style.right = 0;
    myIframe.style.position = 'fixed';
    myIframe.style.width = '375px';
    myIframe.style.zIndex = 9999;
    myIframe.style.height = '100%'; // You can adjust the height as needed
    myIframe.style.boxShadow = '0 0 30px 0 rgba(0, 0, 0, 0.5)';
    myIframe.style.border = '1px';
    myIframe.style.backgroundColor = 'white';
    myIframe.style.direction = 'ltr';
    // Append the iframe to the body
    document.body.appendChild(myIframe);
    var collapseImg=document.getElementById('sa-collaspse-icon');
    collapseImg.style.display="none";
   // setTimeout(extractJobPostingInfoAndSendMessage, 500);
}

initPlugins();
//initFrame();
//document.addEventListener('DOMContentLoaded', extractJobPostingInfoAndSendMessage);
function extractJobPostingInfoAndSendMessage(isGetThisJob) {
    var jobPosting = extractJobPostingInfo();
    var iframe = document.getElementById('postingJobSalaryIframe');
    if (!iframe)
        return;
    if(isGetThisJob){
        iframe.contentWindow.postMessage({isGetThisJob:isGetThisJob,jobPosting:jobPosting} , '*');
    }else{
        iframe.contentWindow.postMessage(jobPosting, '*');
    }

}
window.addEventListener('message', function (event) {
    var response = event.data;
    if (response.command == "CloseJobSalaryExtensionWindow") {
        localStorage.setItem("CloseJobSalaryExtensionWindow", 1);
        var myIframe = document.getElementById('postingJobSalaryIframe');
        if (myIframe) {
            document.body.removeChild(myIframe);
        }
        var collapseImg=document.getElementById('sa-collaspse-icon');
        if(collapseImg){
            collapseImg.style.display="none";
        }
    }
    else if (response.command=="AutoRefresh") {
        localStorage.setItem("CloseJobSalaryExtensionWindow", 1);
        var myIframe = document.getElementById('postingJobSalaryIframe');
        if (myIframe) {
            document.body.removeChild(myIframe);
        }
    }
    else if(response.command=="CollaspseJobSalaryExtensionWindow"){
        var collapseImg=document.getElementById('sa-collaspse-icon');
        var myIframe = document.getElementById('postingJobSalaryIframe');
        if (myIframe) {
            document.body.removeChild(myIframe);
        }
        if(collapseImg){
            collapseImg.style.display="block";
        }
    }
    else if(response.command=="AutoFillDesc"){
        extractJobPostingInfoAndSendMessage();
    }
    else if(response.command=="RedictSalarySite"){
       window.open("https://www.salary.com/request-demo?utm_source=partner&utm_medium=plugin&utm_campaign=daas-plugin&product=Real-time%20Job%20Posting%20Salary%20Data","_blank");
     }
     else if(response.command=="GetSalaryForThisJob"){
        extractJobPostingInfoAndSendMessage(true);
     }

});
function extractJobPostingInfo() {
    const scripts = document.getElementsByTagName('script');
    var jobTitle = "";
    var jobDesc = "";
    var jobPosting = null;
    for (let i = 0; i < scripts.length; i++) {
        let scriptItem = scripts[i];
        if (scriptItem.type !== 'application/ld+json') continue;

        try {
            if (scriptItem.textContent == "")
                continue;
            var textContent = scriptItem.textContent;
            textContent = textContent.replace(/\n+/g, ' ').replace(/\t+/g, ' ');
            var jsonContent = JSON.parse(textContent);
            jsonContent = JSON.parse(textContent);

            // Check if jsonContent is an array
            if (Array.isArray(jsonContent)) {
                for (let j = 0; j < jsonContent.length; j++) {
                    if (jsonContent[j]['@type'] === 'JobPosting') {
                        jobPosting = jsonContent[j];
                        break;
                    }
                }
            } else if (jsonContent['@type'] === 'JobPosting') {
                jobPosting = jsonContent;
                break;
            }
            if (jobPosting != null)
                break;

        } catch (e) {
            console.error('Error parsing JSON:', e);
        }
    }
    if (jobPosting != null) {
        jobTitle = jobPosting.title.trim()
        jobDesc = jobPosting.description;
        jobDesc = new DOMParser().parseFromString(jobDesc, 'text/html').body.textContent.trim();
        if (jobDesc.startsWith("<")) {
            try {
                jobDesc = $(jobDesc).text().trim();
            } catch { }
        }
        return { jobTitle: jobTitle, jobDesc: jobDesc };
    }
    if (location.href.startsWith("https://www.indeed.com")) {
        var element = $('[data-testid="jobsearch-JobInfoHeader-title"] > span');
        jobTitle = element[0].childNodes[0].textContent;
        jobDesc = $('#jobDescriptionText').text();
        return { jobTitle: jobTitle, jobDesc: jobDesc };
    }
     if(jdspiderData!=null){
        let jdspiderJsonData=JSON.parse(jdspiderData);
        for(let i=0;i<jdspiderJsonData.length;i++){
            if(jdspiderJsonData[i].urltype=="1"){
                if (location.href.startsWith(jdspiderJsonData[i].url)) {
                    if(i==3){
                        jobTitle = $($(jdspiderJsonData[i].title)[0]).text();
                        jobDesc = $(jdspiderJsonData[i].desc).text();
                    }else{
                        jobTitle = $(jdspiderJsonData[i].title).text();
                        jobDesc = $(jdspiderJsonData[i].desc).text();
                    }

                }
            }
            if(jdspiderJsonData[i].urltype=="2"){
                if (location.href.indexOf(jdspiderJsonData[i].url) != -1) {
                    jobTitle = $(jdspiderJsonData[i].title).text();
                    jobDesc = $(jdspiderJsonData[i].desc).text();
                }
            }
        }
     }
           /*  if(location.href.indexOf('chitransom')!=-1){
                    jobTitle = $("#lblTitle").text();
                    jobDesc = $("#divJobDetails").text();
                } */
    return { jobTitle: jobTitle.trim(), jobDesc: jobDesc.trim() };
}


/* if(localApiVersion==null){
    fetch('http://localhost:8889/JobSalary/jdspiderversion.txt').then(reponse=>{
    return reponse;
 }).then(response=>response.text()).then(data=>{
    let serverApiVersion=data;
    localStorage.setItem("apiVersion", data);
 })
} */

function GetJDSpiderList(){
    $.ajax({
        url:jobSalaryHost+"/JobSalary/jdspider.json",                                                                                                                                                                                                                                                                     
        type:"GET",
        Headers:{'Cache-Control': 'no-cache'},
        dataType:"json",
        success:function(data){
            localStorage.setItem("jdspiderData", JSON.stringify(data.jobnodes));    
            jdspiderData=localStorage.getItem("jdspiderData");
            for(let i=0;i<data.jobnodes.length;i++){
                arrATSSiteURL.push(data.jobnodes[i].url);
            };
            //localStorage.setItem("arrATSSiteUrl", JSON.stringify(arrATSSiteURL));
            if(arrATSSiteURL.some(url => window.location.href.indexOf(url) > -1)==false &&
        !window.location.href.startsWith('https://www.salary.com/')&& !window.location.href.startsWith('https://www.career.com/')){
                var collapseImg=document.getElementById('sa-collaspse-icon');
               collapseImg.style.display="block";
            }else{
                initFrame();
            }         
        }
    });
}
document.querySelectorAll(".JobCard_jobCardWrapper__lyvNS").forEach(item => {

    item.addEventListener('click',function(){
        initFrame();
    })
})

if(document.getElementById("sa-collaspse-icon")!=null){
    document.getElementById("sa-collaspse-icon").addEventListener('click',function(){
        initFrame();
    })
    
}
   /*  $.ajax({
        url:jsonUrl,                                                                                                                                                                                                                                                                     
        type:"GET",
        Headers:{'Cache-Control': 'no-cache'},
        dataType:"json",
        success:function(data){
            jobSalaryHost=data.jobSalaryHost;
        }
    }); */