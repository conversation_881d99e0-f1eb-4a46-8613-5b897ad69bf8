{"ApiSetting": [{"Enable": false, "ClientID": "05618CBA-5330-4F1B-9726-F89D62FB54D0", "ClientSecret": "BED43FB0-B9E5-4780-9CBE-557E1A61AC77", "TokenUrl": "https://salarystaging40.salarynet.local/token", "GetSalaryUrl": "https://salarystaging40.salarynet.local/DaaS/jobpricing", "SalaryHost": "https://salarystaging61.salarynet.local:9020/"}, {"Enable": false, "ClientID": "05618CBA-5330-4F1B-9726-F89D62FB54D0", "ClientSecret": "BED43FB0-B9E5-4780-9CBE-557E1A61AC77", "TokenUrl": "https://************/token", "GetSalaryUrl": "https://************/DaaS/jobpricing", "SalaryHost": "https://wwwdelta.salary.com"}, {"Enable": true, "ClientID": "A029DDB8-5201-47E2-8949-D0E5CDF27B95", "ClientSecret": "469F752F-9977-44E2-8318-09D5BB9CFF62", "TokenUrl": "https://daasjobmatchapi.salary.com/token", "GetSalaryUrl": "https://daasjobmatchapi.salary.com/DaaS/jobpricing", "SalaryHost": "http://localhost:9020/"}, {"Enable": false, "ClientID": "A029DDB8-5201-47E2-8949-D0E5CDF27B95", "ClientSecret": "469F752F-9977-44E2-8318-09D5BB9CFF62", "TokenUrl": "https://daasjobmatchapi.salary.com/token", "GetSalaryUrl": "https://daasjobmatchapi.salary.com/DaaS/jobpricing", "SalaryHost": "https://www.salary.com/"}, {"Enable": false, "ClientID": "A029DDB8-5201-47E2-8949-D0E5CDF27B95", "ClientSecret": "469F752F-9977-44E2-8318-09D5BB9CFF62", "TokenUrl": "https://************/token", "GetSalaryUrl": "https://************/DaaS/jobpricing", "SalaryHost": "https://www.salary.com/"}, {"Enable": false, "ClientID": "A029DDB8-5201-47E2-8949-D0E5CDF27B95", "ClientSecret": "469F752F-9977-44E2-8318-09D5BB9CFF62", "TokenUrl": "https://*************/token", "GetSalaryUrl": "https://*************/DaaS/jobpricing", "SalaryHost": "https://salarystaging61.salarynet.local:9020/"}]}