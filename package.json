{"name": "job-salary-chrome-plugin-server", "version": "1.0.0", "description": "Local development server for Job Salary Chrome Plugin", "main": "simple-server.js", "scripts": {"start": "node simple-server.js", "dev": "nodemon simple-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["job", "salary", "chrome-extension", "scraping", "api"], "author": "Job Salary Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}