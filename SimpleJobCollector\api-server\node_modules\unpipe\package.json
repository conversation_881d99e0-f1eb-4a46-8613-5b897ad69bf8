{"name": "unpipe", "description": "Unpipe a stream from all destinations", "version": "1.0.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "stream-utils/unpipe", "devDependencies": {"istanbul": "0.3.15", "mocha": "2.2.5", "readable-stream": "1.1.13"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}}