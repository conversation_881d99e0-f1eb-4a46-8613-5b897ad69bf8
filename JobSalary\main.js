﻿$('#GetJobSalary').click(getJobSalary);
$('#CloseWindow').click(closeWindow);
$('#AutoRefresh').change(autoRefresh);
function getJobSalary(isGetThisJob="",jobTitle="",jobDesc="") {
    if(isGetThisJob==true){
        if(jobTitle==""&&jobDesc==""){
            $(".sa-job-info").hide();
            $(".sa-nodata-div").show();
            return;
        }else{
            title=jobTitle;
            jd=jobDesc;    
        }

    }
    else{
         title = document.getElementById('JobTitle').value;
         jd = document.getElementById('JobDesc').value;
         var isVaild=true;
         $(".inputSection").find(".modal-input").each(function (index, element) {
             if($(element).val()===""){
                 isVaild=false;
                 $(element).css({ "border": "1px solid #C32026" });
                 return;
             }
             else{
                 $(element).css({ "border": "1px solid #e6e7e8" });
             }
         });
         if(!isVaild) return;
    }
    var salaryData={
        JobTitle:title,
        JobDesc:jd,
        CompanyName:"",    
        DataScope:{
            CountryCode:'usa',
            City: "",
            FTEValue: 0,
            NAICSCode: "",
            Revenue: 0,State: "",
            ZipCode: ""
        }      
     };
     var tokenUrl="";
     var getSalaryUrl="";
     var salaryHost="";
     var data={
     };
    // call api to get job salary 
    $.getJSON("appsetting.json",function(res){
        for(let i=0;i<res.ApiSetting.length;i++){
            if(res.ApiSetting[i].Enable==true){
                tokenUrl=res.ApiSetting[i].TokenUrl;
                getSalaryUrl=res.ApiSetting[i].GetSalaryUrl;
                data.ClientID=res.ApiSetting[i].ClientID;
                data.ClientSecret=res.ApiSetting[i].ClientSecret;
                salaryHost=res.ApiSetting[i].SalaryHost;
                break;
            }
        }
    }).then(function(){
        var token=bindLocalStorage.get("token");
        if(token!=null){
            GetSalaryFromApi(token,salaryData,getSalaryUrl,salaryHost);
        }else{
            $.ajax({
                url: tokenUrl, //'https://daasjobmatchapi.salary.com/token' ,//
                data:data,
                dataType:'json',
                success:function(res){
                    bindLocalStorage.set(`token`, res.AccessToken.TokenContent,3);
                    GetSalaryFromApi(res.AccessToken.TokenContent,salaryData,getSalaryUrl,salaryHost);

                }
            })
        }
    
    })
}
function closeWindow() {
    parent.postMessage({ command:"CloseJobSalaryExtensionWindow"}, '*');
}

function closeModal() {
    parent.postMessage({ command:"CloseJobSalaryExtensionWindow"}, '*');
}
function expandModal() { 
    parent.postMessage({ command:"CollaspseJobSalaryExtensionWindow"}, '*');
}
function RedictSalarySite(){
    parent.postMessage({ command:"RedictSalarySite"}, '*');
}
function GetSalaryFromApi(token,data,getSalaryUrl,salaryHost){
  
    var Apidata={
        strJsonData:JSON.stringify(data),
        strToken:token,
        strApiUrl:getSalaryUrl
    }
     $.ajax({
        url:salaryHost+"research/salary/JobSalary/SalAjxGetSalaryByPluginFromApi",//getSalaryUrl,
        type:'POST',
        dataType:'json',
        cache:false,
        data:Apidata,
        success:function(res){
            debugger;
            if(res.StatusCode=="-1"){
                $(".sa-job-info").hide();
                $(".sa-nodata-div").show();
            }else{
                $(".confident-match").html(res.MatchRating); 
                $(".sa-job-info").hide();
                $(".sa-salary-info").show();
                $(".sa-salary25").html(res.Salary25);
                $(".sa-salary50").html(res.Salary50);
                $(".sa-salary75").html(res.Salary75);          
            }
  
        }
     })
}
function autoRefresh() {
    parent.postMessage({ command:'AutoRefresh', value:$('#AutoRefresh').is(':checked') }, '*');
}
function GetSalaryForThisJob() {
    parent.postMessage({ command:'GetSalaryForThisJob'}, '*');
}
function autoFillDesc(){
    parent.postMessage({command:'AutoFillDesc'}, "*");
}
function back() {
    $(".sa-job-info").show();
    $(".sa-salary-info").hide();
    $(".sa-nodata-div").hide();
}
window.addEventListener('message', function (event) {
    // Process the message received from the parent
    var response = event.data;
    let  isGetThisJob=response.isGetThisJob;
    if(isGetThisJob==true){
        var thisJobTilte=response.jobPosting.jobTitle;
        var thisJobDesc=response.jobPosting.jobDesc;
        getJobSalary(isGetThisJob, thisJobTilte,thisJobDesc);
    }else{
        document.getElementById('JobTitle').value = response.jobTitle;
        document.getElementById('JobDesc').value = response.jobDesc;
    }

});

function SalPopupCTALinkFormInit() {
    $("#sal_sharelink_popup").modal("show");
}
function copyToClipboard(text) {
    document.getElementById("select_data").select();
    document.execCommand("copy");
}

const bindLocalStorage = {
    set: function (key, value, exdays) {
        var d = new Date();
        d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
        var expires =d.getTime();
        var data = { value: value, expirse: expires };
        localStorage.setItem(key, JSON.stringify(data));
    },
    get: function (key) {
        var data = JSON.parse(localStorage.getItem(key));
        if (data !== null) {
            //debugger
            if (data.expirse != null && data.expirse < new Date().getTime()) {
                localStorage.removeItem(key);
            } else {
                return data.value;
            }
        }
        return null;
    }
}


