function injectScripts(tab) {
    if (!tab.url || !tab.url.startsWith("http"))
        return;

    chrome.scripting.executeScript({
        target: { tabId: tab.id },
        files: ['jquery.min.js', 'contentScript.js']
    });
}

chrome.action.onClicked.addListener((tab) => {
    injectScripts(tab);
});

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete') {
        injectScripts(tab);
    }
});
