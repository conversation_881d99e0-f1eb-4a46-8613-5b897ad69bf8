// Configuration
const API_BASE_URL = 'http://localhost:3000';

// DOM Elements
const extractBtn = document.getElementById('extract-btn');
const submitBtn = document.getElementById('submit-btn');
const jobTitleInput = document.getElementById('job-title');
const jobDescInput = document.getElementById('job-description');
const jobUrlInput = document.getElementById('job-url');
const statusIndicator = document.getElementById('status-indicator');
const statusText = document.getElementById('status-text');
const resultsSection = document.getElementById('results-section');
const resultsText = document.getElementById('results-text');
const serverStatus = document.getElementById('server-status');
const viewAllJobsLink = document.getElementById('view-all-jobs');
const clearFormLink = document.getElementById('clear-form');

// State
let currentTab = null;
let extractedData = null;

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
    await initializePopup();
    setupEventListeners();
    checkServerStatus();
});

async function initializePopup() {
    try {
        // Get current tab
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        currentTab = tab;
        
        // Set current URL
        jobUrlInput.value = tab.url;
        
        // Update status
        updateStatus('ready', 'Ready to extract job data');
        
    } catch (error) {
        console.error('Error initializing popup:', error);
        updateStatus('error', 'Error initializing extension');
    }
}

function setupEventListeners() {
    extractBtn.addEventListener('click', extractJobData);
    submitBtn.addEventListener('click', submitJobData);
    clearFormLink.addEventListener('click', clearForm);
    viewAllJobsLink.addEventListener('click', viewAllJobs);
    
    // Enable submit button when form has data
    [jobTitleInput, jobDescInput].forEach(input => {
        input.addEventListener('input', validateForm);
    });
}

async function extractJobData() {
    try {
        updateStatus('extracting', 'Extracting job data...');
        extractBtn.disabled = true;

        // Try to send message to content script first
        try {
            const response = await chrome.tabs.sendMessage(currentTab.id, { action: 'extractJobData' });
            if (response && (response.jobTitle || response.jobDescription)) {
                extractedData = response;

                // Populate form fields
                jobTitleInput.value = extractedData.jobTitle || '';
                jobDescInput.value = extractedData.jobDescription || '';
                console.log("can u see me?");    
                updateStatus('success', 'Job data extracted successfully! 222');
                validateForm();
                return;
            }
        } catch (contentScriptError) {
            console.log('Content script not available, using direct injection');
        }

        // Fallback: inject extraction function directly
        const [result] = await chrome.scripting.executeScript({
            target: { tabId: currentTab.id },
            function: extractJobFromPage
        });

        if (result && result.result) {
            extractedData = result.result;

            // Populate form fields
            jobTitleInput.value = extractedData.jobTitle || '';
            jobDescInput.value = extractedData.jobDescription || '';

            if (extractedData.jobTitle || extractedData.jobDescription) {
                updateStatus('success', 'Job data extracted successfully! 111');
                validateForm();
            } else {
                updateStatus('error', 'No job data found on this page');
            }
        } else {
            updateStatus('error', 'Failed to extract job data');
        }

    } catch (error) {
        console.error('Error extracting job data:', error);
        updateStatus('error', `Error extracting job data: ${error.message}`);
    } finally {
        extractBtn.disabled = false;
    }
}

async function submitJobData() {
    try {
        const jobData = {
            jobTitle: jobTitleInput.value.trim(),
            jobDescription: jobDescInput.value.trim(),
            jobUrl: jobUrlInput.value,
            source: getJobSource(jobUrlInput.value)
        };
        
        if (!jobData.jobTitle || !jobData.jobDescription) {
            showResults('error', 'Please fill in both job title and description');
            return;
        }
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="loading"></span> Submitting...';
        
        const response = await fetch(`${API_BASE_URL}/api/jobs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(jobData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showResults('success', 'Job data submitted successfully!');
            updateStatus('success', 'Data submitted to server');
            
            // Clear form after successful submission
            setTimeout(() => {
                clearForm();
            }, 2000);
        } else {
            showResults('error', result.message || 'Failed to submit job data');
        }
        
    } catch (error) {
        console.error('Error submitting job data:', error);
        showResults('error', 'Error connecting to API server');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<span class="btn-icon">📤</span> Submit Job Data';
    }
}

function validateForm() {
    const hasTitle = jobTitleInput.value.trim().length > 0;
    const hasDesc = jobDescInput.value.trim().length > 0;
    submitBtn.disabled = !(hasTitle && hasDesc);
}

function clearForm() {
    jobTitleInput.value = '';
    jobDescInput.value = '';
    extractedData = null;
    submitBtn.disabled = true;
    resultsSection.style.display = 'none';
    updateStatus('ready', 'Ready to extract job data');
}

function updateStatus(type, message) {
    statusIndicator.className = `status-indicator ${type}`;
    statusText.textContent = message;
}

function showResults(type, message) {
    const resultsBox = resultsSection.querySelector('.results-box');
    resultsBox.className = `results-box ${type}`;
    resultsText.textContent = message;
    resultsSection.style.display = 'block';
}

function getJobSource(url) {
    try {
        const hostname = new URL(url).hostname;
        return hostname.replace('www.', '');
    } catch {
        return 'Unknown';
    }
}

async function checkServerStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/health`);
        if (response.ok) {
            serverStatus.textContent = '🟢 Server connected';
            serverStatus.style.color = '#28a745';
        } else {
            throw new Error('Server not responding');
        }
    } catch (error) {
        serverStatus.textContent = '🔴 Server offline';
        serverStatus.style.color = '#dc3545';
    }
}

function viewAllJobs() {
    chrome.tabs.create({ url: `${API_BASE_URL}/api/jobs` });
}

// This function will be injected into the page to extract job data
function extractJobFromPage() {
    console.log("hehe");
    // Job site selectors (simplified version of jdspider.json)
    const selectors = [
        // LinkedIn
        {
            url: 'linkedin.com/jobs',
            title: 'div[class*="job-details-jobs-unified-top-card__job-title"]',
            desc: 'div[class*="jobs-description-content__text"]'
        },
        // Indeed
        {
            url: 'indeed.com',
            title: '[data-testid="jobsearch-JobInfoHeader-title"] span, h1[class*="jobsearch-JobInfoHeader-title"]',
            desc: '#jobDescriptionText, div[class*="jobsearch-jobDescriptionText"]'
        },
        // Glassdoor
        {
            url: 'glassdoor.com',
            title: '[id*="jd-job-title"], h1[class*="job-title"]',
            desc: '[class*="JobDetails_jobDescription"], div[class*="job-description"]'
        },
        // ZipRecruiter
        {
            url: 'ziprecruiter.com',
            title: 'h1[class*="job_title"], h1[class*="font-bold"]',
            desc: 'div[class*="job_description"], div[class*="rounded-8"]'
        },
        // Monster
        {
            url: 'monster.com',
            title: 'h2[data-testid="jobTitle"], h1[class*="job-title"]',
            desc: 'div[class*="code-styles__CodeContainer"], div[class*="job-description"]'
        },
        // Generic fallback
        {
            url: '',
            title: 'h1, h2, [class*="job-title"], [class*="title"]',
            desc: '[class*="description"], [class*="job-desc"], [id*="description"]'
        }
    ];
    
    const currentUrl = window.location.href;
    let jobTitle = '';
    let jobDescription = '';
    
    // Find matching selector
    const matchingSelector = selectors.find(selector => 
        selector.url === '' || currentUrl.includes(selector.url)
    );
    
    if (matchingSelector) {
        // Extract title
        const titleElement = document.querySelector(matchingSelector.title);
        if (titleElement) {
            jobTitle = titleElement.textContent.trim();
        }
        
        // Extract description
        const descElement = document.querySelector(matchingSelector.desc);
        if (descElement) {
            jobDescription = descElement.textContent.trim();
        }
    }
    
    // Fallback: try to find job posting structured data
    if (!jobTitle || !jobDescription) {
        const scripts = document.querySelectorAll('script[type="application/ld+json"]');
        for (const script of scripts) {
            try {
                const data = JSON.parse(script.textContent);
                const jobPosting = Array.isArray(data) 
                    ? data.find(item => item['@type'] === 'JobPosting')
                    : data['@type'] === 'JobPosting' ? data : null;
                
                if (jobPosting) {
                    if (!jobTitle && jobPosting.title) {
                        jobTitle = jobPosting.title.trim();
                    }
                    if (!jobDescription && jobPosting.description) {
                        jobDescription = jobPosting.description.replace(/<[^>]*>/g, '').trim();
                    }
                    break;
                }
            } catch (e) {
                // Continue to next script
            }
        }
    }
    
    return {
        jobTitle: jobTitle || '',
        jobDescription: jobDescription || '',
        url: currentUrl,
        extractedAt: new Date().toISOString()
    };
}
