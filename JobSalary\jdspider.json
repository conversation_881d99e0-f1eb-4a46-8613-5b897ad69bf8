{"jobnodes": [{"url": "https://www.indeed.com", "urltype": "1", "iframe": "", "title": "[data-testid=\"jobsearch-JobInfoHeader-title\"] > span", "desc": "#jobDescriptionText"}, {"url": "https://www.glassdoor.com", "urltype": "1", "iframe": "", "title": "[id^=\"jd-job-title-\"]", "desc": "[class^=\"JobDetails_jobDescription_\"]"}, {"url": "https://www.linkedin.com/jobs/", "urltype": "1", "iframe": "", "title": "h2[class^=\"top-card-layout__title\"]", "desc": "div[class^=\"description__text\"]"}, {"url": "https://www.ziprecruiter.com/jobs-search", "urltype": "1", "iframe": "", "title": "h1[class^=\"font-bold text-black text-header-lg md:text-header-lg-tablet\"]", "desc": "div[class^=\"rounded-8 bg-light-gray flex flex-col gap-24 p-36\"]"}, {"url": "https://www.ziprecruiter.com/jobs/", "urltype": "1", "iframe": "", "title": "h1[class^=\"job_title\"]", "desc": "div[class^=\"job_description\"]"}, {"url": "https://www.monster.com/jobs/", "urltype": "1", "iframe": "", "title": "h2[data-testid=\"jobTitle\"]", "desc": "div[class^=\"code-styles__CodeContainer\"]"}, {"url": "https://www.talent.com/jobs", "urltype": "1", "iframe": "", "title": "div[class^=\"jobPreview__header--title\"]", "desc": "div[class^=\"jobPreview__body--description\"]"}, {"url": "https://jobs.localjobnetwork.com/job", "urltype": "1", "iframe": "", "title": "[itemprop=\"title\"]", "desc": "[itemprop=\"description\"]"}, {"url": "https://recruiting.adp.com/", "urltype": "1", "iframe": "", "title": "span[class^=\"jobTitle job-detail-title\"]", "desc": "div[class^=\"container-fluid\"]"}, {"url": "https://workforcenow.adp.com/", "urltype": "1", "iframe": "", "title": "[class^=\"job-description-title\"]", "desc": "[class^=\"job-description-data\"]"}, {"url": "https://recruiting2.ultipro.com/", "urltype": "1", "iframe": "", "title": "[data-bind=\"text: formattedTitle\"]", "desc": "[data-bind=\"html: Description\"]"}, {"url": "https://www.dice.com/job-detail/", "urltype": "1", "iframe": "", "title": "[data-cy=\"jobTitle\"]", "desc": "#jobDescription"}, {"url": "https://jobs.smartrecruiters.com/", "urltype": "1", "iframe": "", "title": "[itemprop=\"title\"]", "desc": "[itemprop=\"description\"]"}, {"url": "https://www.liquidcompass.com/results", "urltype": "1", "iframe": "", "title": "h1[class^=\"ng-binding\"]", "desc": "#job-description"}, {"url": "https://www.schoolspring.com", "urltype": "1", "iframe": "", "title": "[class^=\"job-details-job-title\"]", "desc": "#jobDescription-value"}, {"url": "https://www.google.com/search", "urltype": "1", "iframe": "", "title": "#tl_ditsc$$$h2", "desc": "#tl_ditsc$$$div[jsaction^=\"iJE3Ge:\"]"}, {"url": ".icims.com/jobs/", "urltype": "2", "iframe": "#icims_content_iframe", "title": "h1[class^=\"iCIMS_Header\"]", "desc": "div[class^=\"iCIMS_JobContainer\"]"}, {"url": ".hrmdirect.com/employment/job-opening", "urltype": "2", "iframe": "", "title": "h2", "desc": "[class^=\"jobDesc\"]"}, {"url": "https://www.thejobnetwork.com/jobs", "urltype": "1", "iframe": "", "title": "[data-testid=\"title\"]", "desc": "[data-testid=\"job-detail-description\"]"}, {"url": ".thejobnetwork.com/job/", "urltype": "2", "iframe": "", "title": "#jobTitleText", "desc": "#JobDesc"}, {"url": "https://www.careerbuilder.com/", "urltype": "1", "iframe": "", "title": "[class*=\"jdp_title_header\"]", "desc": "#jdp_description"}, {"url": "https://www.simplyhired.com/", "urltype": "1", "iframe": "", "title": "[data-testid=\"viewJobTitle\"]", "desc": "[data-testid=\"viewJobBodyJobFullDescriptionContent\"]"}, {"url": ".oraclecloud.com/", "urltype": "2", "iframe": "", "title": "h1", "desc": "div[class*=\"job-details__description-content\"]"}, {"url": ".mojo.myisolved.com/social#user/", "urltype": "2", "iframe": "", "title": "h1 + p[class^=\"desg\"]", "desc": "div[class^=\"job-responsibility\"]"}]}