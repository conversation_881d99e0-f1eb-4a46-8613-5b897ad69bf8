// Simple Node.js server for local development
// This replaces the need for IIS/ASP.NET for basic testing

const express = require('express');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 8889; // Matches the default port in contentScript.js

// Enable CORS for all routes
app.use(cors());

// Parse JSON bodies
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from JobSalary directory
app.use('/JobSalary', express.static(path.join(__dirname, 'JobSalary')));

// Serve static files from ChromePlugin directory  
app.use('/ChromePlugin', express.static(path.join(__dirname, 'ChromePlugin')));

// Serve the main interface
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'JobSalary', 'index.html'));
});

// API endpoint to get job salary (simplified version)
app.get('/JobSalary/GetJobSalary.aspx', (req, res) => {
    const jobTitle = req.query.JobTitle || '';
    const jobDesc = req.query.JobDesc || '';
    
    console.log('Job Title:', jobTitle);
    console.log('Job Description:', jobDesc.substring(0, 100) + '...');
    
    // Mock response - replace with actual API call
    const mockResponse = `
        <table border='1'>
            <tr><th>YearMonth</th><th>Salary25</th><th>Salary50</th><th>Salary75</th></tr>
            <tr><td>202410</td><td>$75,000</td><td>$95,000</td><td>$120,000</td></tr>
            <tr><td>202409</td><td>$74,000</td><td>$94,000</td><td>$118,000</td></tr>
            <tr><td>202408</td><td>$73,000</td><td>$93,000</td><td>$117,000</td></tr>
        </table>
    `;
    
    res.send(mockResponse);
});

// API endpoint for salary data (JSON format)
app.post('/research/salary/JobSalary/SalAjxGetSalaryByPluginFromApi', (req, res) => {
    console.log('Salary API called with:', req.body);
    
    // Mock salary response
    const mockSalaryData = {
        StatusCode: "0",
        MatchRating: "Confident Match",
        Salary25: "$75,000",
        Salary50: "$95,000", 
        Salary75: "$120,000"
    };
    
    res.json(mockSalaryData);
});

// Serve jdspider.json with proper headers
app.get('/JobSalary/jdspider.json', (req, res) => {
    res.setHeader('Cache-Control', 'no-cache');
    res.sendFile(path.join(__dirname, 'JobSalary', 'jdspider.json'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        message: 'Job Salary Chrome Plugin Server is running',
        port: PORT 
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ error: 'Internal server error' });
});

// Start the server
app.listen(PORT, () => {
    console.log(`
🚀 Job Salary Chrome Plugin Server is running!

📍 Server URL: http://localhost:${PORT}
🌐 Main Interface: http://localhost:${PORT}/JobSalary/index.html
📋 Job Sites Config: http://localhost:${PORT}/JobSalary/jdspider.json
🔧 Health Check: http://localhost:${PORT}/health

📝 Next Steps:
1. Install Chrome extension from ChromePlugin folder
2. Update Chrome extension to point to http://localhost:${PORT}
3. Test on supported job sites (LinkedIn, Indeed, etc.)

💡 To stop the server: Press Ctrl+C
    `);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});

module.exports = app;
