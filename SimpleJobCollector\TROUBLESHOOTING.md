# Chrome Extension Troubleshooting Guide

## 🔧 Fixed Issues Summary

### **1. Missing Permissions (FIXED)**
**Problem:** `chrome.alarms` and `chrome.scripting` APIs were undefined
**Solution:** Added missing permissions to `manifest.json`:
```json
"permissions": [
  "activeTab",
  "storage",
  "scripting",    // ← Added for chrome.scripting API
  "tabs",         // ← Added for chrome.tabs API  
  "alarms"        // ← Added for chrome.alarms API
]
```

### **2. Simplified Background Script (FIXED)**
**Problem:** Complex background script with non-essential features causing errors
**Solution:** Reduced background.js to only essential functions:
- ✅ Extension installation handler
- ✅ Message handling for API status checks
- ✅ Storage management
- ❌ Removed: Automatic content script injection
- ❌ Removed: Tab monitoring
- ❌ Removed: Alarm-based cleanup
- ❌ Removed: Icon click handlers

### **3. Improved Job Data Extraction (FIXED)**
**Problem:** `chrome.scripting.executeScript` errors in popup
**Solution:** Dual approach for job extraction:
1. **Primary:** Try content script message passing
2. **Fallback:** Direct script injection if content script unavailable

## 🎯 Essential vs Optional Functions

### **Essential Functions (Core Functionality):**
1. **Extension Installation** - Sets up default settings
2. **API Status Check** - Verifies server connection
3. **Job Data Extraction** - Core feature for getting job info
4. **Data Submission** - Sends data to API server
5. **Storage Management** - Saves user preferences

### **Optional Functions (Removed for Reliability):**
1. **Automatic Content Script Injection** - Not needed with manifest content_scripts
2. **Tab Monitoring** - Adds complexity without core benefit
3. **Periodic Cleanup** - Nice-to-have but not essential
4. **Icon Click Handling** - Redundant with popup interface

## 🚀 Testing the Fixed Extension

### **Step 1: Reload the Extension**
1. Go to `chrome://extensions/`
2. Find "Simple Job Data Collector"
3. Click the refresh icon
4. Check for any error messages

### **Step 2: Test Basic Functionality**
1. Go to LinkedIn Jobs: https://www.linkedin.com/jobs/
2. Open any job posting
3. Click extension icon
4. Click "Get Job Data"
5. Verify data appears in form fields
6. Click "Submit" to test API connection

### **Step 3: Check for Errors**
1. **Extension Console:**
   - Right-click extension icon → "Inspect popup"
   - Check Console tab for errors

2. **Background Script Console:**
   - Go to `chrome://extensions/`
   - Click "Inspect views: background page"
   - Check Console tab

3. **Content Script Console:**
   - On job posting page, press F12
   - Check Console tab for content script errors

## 🐛 Common Issues & Solutions

### **Issue: "Cannot read properties of undefined"**
**Cause:** Missing permissions or API not available
**Solution:** 
1. Verify all permissions are in manifest.json
2. Reload extension after permission changes
3. Check Chrome version compatibility

### **Issue: "Extension context invalidated"**
**Cause:** Extension was reloaded while popup was open
**Solution:**
1. Close popup
2. Reload extension
3. Reopen popup

### **Issue: "No job data found"**
**Cause:** Website changed HTML structure or unsupported site
**Solution:**
1. Test on known working sites (LinkedIn, Indeed)
2. Check browser console for selector errors
3. Update selectors in content script if needed

### **Issue: "Server offline" status**
**Cause:** API server not running or wrong port
**Solution:**
1. Verify server is running: `npm start` in api-server folder
2. Check server URL in popup.js matches actual server
3. Test server directly: http://localhost:3000/health

## 🔍 Debugging Tips

### **1. Enable Verbose Logging**
Add to popup.js for more detailed logs:
```javascript
console.log('Current tab:', currentTab);
console.log('Extracted data:', extractedData);
console.log('API response:', result);
```

### **2. Test Individual Components**
- **API Server:** `curl http://localhost:3000/health`
- **Job Extraction:** Use browser console on job page
- **Extension Popup:** Inspect popup for UI issues

### **3. Check Network Requests**
1. Open popup
2. Press F12 → Network tab
3. Try submitting data
4. Check for failed API requests

## 📋 Verification Checklist

After applying fixes, verify:

- [ ] Extension loads without errors in `chrome://extensions/`
- [ ] Background script console shows no errors
- [ ] Popup opens and displays correctly
- [ ] Server status shows "🟢 Server connected"
- [ ] "Get Job Data" extracts information on LinkedIn
- [ ] Form fields populate with extracted data
- [ ] "Submit" button sends data to API successfully
- [ ] API server receives and stores job data

## 🔄 If Issues Persist

### **1. Complete Reset**
```bash
# Remove and reinstall extension
1. Go to chrome://extensions/
2. Remove "Simple Job Data Collector"
3. Restart Chrome
4. Load extension again from folder
```

### **2. Check Chrome Version**
- Extension requires Chrome 88+ for Manifest V3
- Update Chrome if using older version

### **3. Test on Different Sites**
- LinkedIn: https://www.linkedin.com/jobs/
- Indeed: https://www.indeed.com/
- Glassdoor: https://www.glassdoor.com/

### **4. Verify File Structure**
```
chrome-extension/
├── manifest.json     ✓ Updated with all permissions
├── popup.html        ✓ Should load correctly
├── popup.js          ✓ Updated with dual extraction approach
├── content.js        ✓ Should handle message passing
├── background.js     ✓ Simplified to essential functions only
└── styles.css        ✓ Should style popup correctly
```

## 🎯 Success Indicators

When everything is working correctly, you should see:
1. **No errors** in any console
2. **Green server status** in popup
3. **Successful data extraction** on job sites
4. **Data submission** working to API
5. **Job data stored** in `api-server/data/jobs.json`

The extension is now simplified and focused on core functionality, making it much more reliable and easier to debug.
