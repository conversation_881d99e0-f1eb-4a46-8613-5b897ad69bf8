// Simple Job Data Collector - Background Script
// Handles essential extension functionality only

console.log('Simple Job Data Collector background script loaded');

// Extension installation handler (ESSENTIAL)
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Simple Job Data Collector installed/updated');

    if (details.reason === 'install') {
        // Set default settings
        chrome.storage.local.set({
            apiUrl: 'http://localhost:3000',
            lastUpdated: Date.now()
        });
        console.log('Extension installed - default settings saved');
    }
});

// Handle messages from popup (ESSENTIAL)
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Background received message:', request);

    switch (request.action) {
        case 'checkApiStatus':
            // Check API server status
            checkApiStatus().then(sendResponse);
            return true; // Keep message channel open for async response

        case 'getStoredData':
            // Get stored settings
            chrome.storage.local.get(request.keys || null, sendResponse);
            return true;

        default:
            console.warn('Unknown action:', request.action);
    }
});

// Check API server status (ESSENTIAL)
async function checkApiStatus() {
    try {
        const { apiUrl } = await chrome.storage.local.get(['apiUrl']);
        const url = apiUrl || 'http://localhost:3000';

        const response = await fetch(`${url}/health`);

        if (response.ok) {
            const data = await response.json();
            return {
                status: 'online',
                message: data.message || 'API server is running',
                url: url
            };
        } else {
            return {
                status: 'error',
                message: 'API server returned error',
                url: url
            };
        }
    } catch (error) {
        return {
            status: 'offline',
            message: 'Cannot connect to API server',
            error: error.message
        };
    }
}

console.log('Background script initialized successfully');
