<%@ Page Language="C#" ValidateRequest="False" %>

<%@ Import Namespace="System.IO" %>
<%@ Import Namespace="System.Net" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%
    var jobTitle = Request["JobTitle"];
    var jobDesc = Request["JobDesc"];
    var salaryDict=Application["JobSalaryTrendingDict"] as Dictionary<string, List<string[]>>;
    if (salaryDict == null)
    {
        salaryDict = new Dictionary<string, List<string[]>>();
        var sr = new StreamReader("F:\\WebSite\\JobSalary\\JobSalaryTrading.txt");
        sr.ReadLine();
        while (!sr.EndOfStream)
        {
            var fields = sr.ReadLine().Split('\t');
            var jobCode = fields[0];
            List<string[]> list = new List<string[]>();
            if (salaryDict.ContainsKey(jobCode))
                list = salaryDict[jobCode];
            else
                salaryDict.Add(jobCode, list);
            list.Add(fields);
        }
        sr.Close();
    }
    ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls | SecurityProtocolType.Ssl3;
    ServicePointManager.ServerCertificateValidationCallback += (a, b, c, d) => true;
    var apiUrl = "http://salarystaging39.salarynet.local:8100/JobMatchAI";
    object obj = new { jobTitle = jobTitle, jobDesc = jobDesc };
    var request = HttpWebRequest.Create(apiUrl);
    request.Method = "POST";
    request.Timeout = 300000;
    request.ContentType = "application/json";

    var data = new System.Web.Script.Serialization.JavaScriptSerializer(){ MaxJsonLength = int.MaxValue }.Serialize(obj);
    var stream = request.GetRequestStream();
    var buff = Encoding.UTF8.GetBytes(data);
    stream.Write(buff, 0, buff.Length);
    var response = request.GetResponse();
    var result = new StreamReader(response.GetResponseStream()).ReadToEnd();
    response.Close();
    var dict = new System.Web.Script.Serialization.JavaScriptSerializer(){ MaxJsonLength = int.MaxValue }.Deserialize<Dictionary<string, object>>(result);
    var bestScore = 0;
    var bestJobCode = "";

    foreach (Dictionary<string, object> item in dict["data"] as ArrayList)
    {
        var score = Convert.ToDouble(item["score"]);
        if (score <= bestScore)
            continue;
        bestJobCode = item["jobCode"].ToString();
    }

    var sb = new StringBuilder();
    sb.AppendLine("<table border='1'><th>YearMonth</th><th>Salary25</th><th>Salary50</th><th>Salary75</th>");
    try
    {
        List<string[]> resultList = salaryDict[bestJobCode];
        for(var j= 48;j<60;j++)
        {
            var fields = resultList[j];
            if (fields[1] == "202406")
                sb.Append("<tr style='background-color:yellow'>");
            else
                sb.Append("<tr>");
            for (var i = 1; i < fields.Length; i++)
            {
                sb.Append("<td>" + fields[i] + "</td>");
            }
            sb.AppendLine("</tr>");
        }
        sb.AppendLine("</table>");
        Response.Write(sb.ToString());
    }
    catch (Exception e)
    {
        throw new Exception(bestJobCode + e.Message);
    }
%>
