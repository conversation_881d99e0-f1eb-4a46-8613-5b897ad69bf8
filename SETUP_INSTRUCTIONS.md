# Job Data Collection System - Setup Guide

## System Overview

This is a job information scraping system with two main components:
- **Chrome Extension**: Provides UI for data collectors to extract job information
- **Web API Server**: Handles website-specific JSON selectors and stores job data

## Prerequisites

### For Backend (ASP.NET/IIS)
- Windows Server or Windows 10/11 with IIS enabled
- .NET Framework 4.5+ 
- IIS with ASP.NET support enabled

### For Development/Testing
- Visual Studio or VS Code
- Chrome browser for extension testing

## Backend Setup Instructions

### Step 1: Enable IIS and ASP.NET

1. **Enable IIS on Windows:**
   - Open "Turn Windows features on or off"
   - Check "Internet Information Services"
   - Under IIS → World Wide Web Services → Application Development Features:
     - Check "ASP.NET 4.8" (or latest version)
     - Check ".NET Extensibility 4.8"

2. **Verify IIS Installation:**
   - Open browser and go to `http://localhost`
   - You should see the IIS welcome page

### Step 2: Deploy the JobSalary Application

1. **Create IIS Application:**
   ```cmd
   # Open IIS Manager (inetmgr)
   # Right-click "Default Web Site" → Add Application
   # Alias: JobSalaryChromePlugin
   # Physical Path: [Your project path]\JobSalary
   ```

2. **Set Permissions:**
   - Right-click the JobSalary folder → Properties → Security
   - Add "IIS_IUSRS" with Read & Execute permissions
   - Add "IUSR" with Read permissions

3. **Configure Application Pool:**
   - In IIS Manager → Application Pools
   - Select DefaultAppPool → Advanced Settings
   - Set ".NET CLR Version" to v4.0
   - Set "Managed Pipeline Mode" to Integrated

### Step 3: Configure the Application

1. **Update Configuration Files:**
   
   **JobSalary/appsetting.json** - Configure API endpoints:
   ```json
   {
       "ApiSetting": [
           {
               "Enable": true,
               "ClientID": "A029DDB8-5201-47E2-8949-D0E5CDF27B95",
               "ClientSecret": "469F752F-9977-44E2-8318-09D5BB9CFF62",
               "TokenUrl": "https://daasjobmatchapi.salary.com/token",
               "GetSalaryUrl": "https://daasjobmatchapi.salary.com/DaaS/jobpricing",
               "SalaryHost": "http://localhost/"
           }
       ]
   }
   ```

2. **Update Chrome Extension Configuration:**
   
   **ChromePlugin/contentScript.js** - Update line 3:
   ```javascript
   var jobSalaryHost = "http://localhost/JobSalaryChromePlugin";
   ```

### Step 4: Test the Backend

1. **Test Basic Functionality:**
   - Open browser: `http://localhost/JobSalaryChromePlugin/JobSalary/index.html`
   - You should see the salary plugin interface

2. **Test JSON Configuration:**
   - Open: `http://localhost/JobSalaryChromePlugin/JobSalary/jdspider.json`
   - Should return the job site configuration JSON

3. **Test ASPX Endpoint:**
   - Open: `http://localhost/JobSalaryChromePlugin/JobSalary/GetJobSalary.aspx?JobTitle=Developer&JobDesc=Software%20Developer`
   - Should return salary data (may show error if external API is not accessible)

## Chrome Extension Setup

### Step 1: Load Extension in Chrome

1. **Enable Developer Mode:**
   - Open Chrome → Settings → Extensions
   - Toggle "Developer mode" ON

2. **Load Extension:**
   - Click "Load unpacked"
   - Select the `ChromePlugin` folder
   - Extension should appear in your extensions list

### Step 2: Configure Extension

1. **Update Host Configuration:**
   - Edit `ChromePlugin/appsetting.json`:
   ```json
   {
       "jobSalaryHost": "http://localhost/JobSalaryChromePlugin"
   }
   ```

### Step 3: Test Extension

1. **Test on Supported Sites:**
   - Go to LinkedIn Jobs: https://www.linkedin.com/jobs/
   - Open any job posting
   - Click the extension icon or look for the floating salary icon
   - The extension should extract job title and description

## Configuration System Explained

### jdspider.json Structure

The `JobSalary/jdspider.json` file contains website-specific selectors:

```json
{
    "jobnodes": [
        {
            "url": "https://www.linkedin.com/jobs/",
            "urltype": "1",
            "iframe": "",
            "title": "h2[class^=\"top-card-layout__title\"]",
            "desc": "div[class^=\"description__text\"]"
        }
    ]
}
```

**Field Explanations:**
- `url`: Website URL pattern to match
- `urltype`: "1" = exact URL match, "2" = contains URL pattern
- `iframe`: CSS selector for iframe (if job content is in iframe)
- `title`: CSS selector for job title element
- `desc`: CSS selector for job description element

### Adding New Job Sites

To add support for a new job board:

1. **Analyze the Website:**
   - Open browser developer tools (F12)
   - Find CSS selectors for job title and description

2. **Add Configuration:**
   ```json
   {
       "url": "https://newjobsite.com/jobs/",
       "urltype": "1", 
       "iframe": "",
       "title": ".job-title-selector",
       "desc": ".job-description-selector"
   }
   ```

3. **Update Extension Permissions:**
   - Add the new domain to `ChromePlugin/manifest.json` host_permissions

## Troubleshooting

### Common Issues

1. **Extension Not Loading:**
   - Check Chrome developer console for errors
   - Verify manifest.json syntax
   - Ensure all files are in correct locations

2. **Backend Not Accessible:**
   - Verify IIS is running
   - Check Windows Firewall settings
   - Ensure ASP.NET is properly installed

3. **CORS Errors:**
   - Verify web.config has proper CORS headers
   - Check browser network tab for failed requests

4. **Job Data Not Extracting:**
   - Website may have changed their HTML structure
   - Update CSS selectors in jdspider.json
   - Check browser console for JavaScript errors

### Development Tips

1. **Testing Selectors:**
   - Use browser console: `document.querySelector("your-selector")`
   - Test selectors on actual job posting pages

2. **Debugging Extension:**
   - Open Chrome DevTools on extension popup
   - Check background script errors in Extensions page

3. **API Testing:**
   - Use Postman or similar tool to test backend endpoints
   - Check IIS logs for server errors

## Next Steps

Once the system is running:
1. Test on multiple job sites
2. Verify data extraction accuracy
3. Configure additional job boards as needed
4. Implement enhanced features as required

## Security Notes

- The system uses external APIs (salary.com)
- Ensure proper API key management
- Consider implementing rate limiting
- Review CORS settings for production use
