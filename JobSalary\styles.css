*{
  margin:0;
  padding:0;
}
html{
  height:100%;
}

body {
  background-color: #f8f8f9 !important;
  min-height:100%;
  margin:0;
  padding:0;
  position:relative;
  font-family: Arial, sans-serif;
  text-align: center;
  margin: 20px;
}
p{
  font-size: 12px !important;
}
.margin-top10{
  margin-top: 10px;
}
.margin-top15{
  margin-top: 15px;
}
.text-size12px{
  font-size: 12px !important;
}
button {
  padding: 10px 20px;
  font-size: 16px;
  margin-top: 20px;
}
.sa-plugs-container{
  width: 375px;
/*   background-color: #f8f8f9 !important;
  border-left: 1px solid #e6e7e8;
  box-shadow: 0 1px 6px rgb(0 0 0 / 10%);
  height: 100%; */
}
.salaryPicture{
  display: flex;
  align-items: center;
  background-color: white;
  border-top: 10px solid #6fbe44;
  height: 80px;
}
.salaryPicture span{
  width: 238px;
    margin-left: 10px;
    font-weight: 700;
    color: #075484;
    font-size: 14px;
}
.inputSection{
  width: 355px;
  position: relative;
  left: 10px;
  border-color: #e6e7e8;
  border: 1px solid #e6e7e8;
  background-color: white;
  margin-top: 10px;
  border-radius: 5px;
}
.sa-paragraph{
font-weight:600;
color:#212221;
font-size:14px;
line-height:20px;
  margin: 16px 10px;
  text-align: left;
}
.button-color{
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  background:#057dbb;
  border-radius:16px;
  width:338px;
  height: 32px;
  border: none;

}
.form-control{
  width: 97% !important;
  display: block;
  min-width: 90%;
  height: 33px;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.2;
  color: #212221;
  background-color: white;
  background-image: none;
  border: 1px solid #cccccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
textarea.form-control {
  height: auto;
  width: 97%;
}
.text-charcoal{
  color: #58595b;
  font-style: normal;
}
.text-black{
  color:#212221;
}
.font-semibold {
  font-style: normal;
  font-weight: 600;
}
.margin-bottom15px{
  margin-bottom: 15px;
}
.margin-botton30px{
  margin-bottom: 30px;
}
.back-position{
    text-align: left;
    margin-top: 10px;
    margin-bottom: 10px;
}
.icon-triangle-left{
    height: 25px;
    position: relative;
    left: 6px;
}
.icon-triangle-down,
.icon-triangle-right,
.icon-triangle-left{
  height: 25px;
  display: block;
  position: relative;
  left: 3px;
}
.match-rating{
  display: flex;
  align-items: center;
  border-color: #e6e7e8;
  border: 1px solid #e6e7e8;
  background-color: white;
  width: 355px;
  padding-top: 15px;
  padding-bottom: 15px;
  border-radius: 5px;
}
.text-size12px{
  font-size: 12px;
}
.text-size15px{
  font-size: 15px;
}
.confident-match{
  font-size: 14px;
  font-weight:600;
}
.text-lightblue{
  color: #007dbc;
}
.compensation{
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 15px;
}
.recommend{
  padding-top: 10px;
    margin-bottom: 10px;
    font-weight: 600;
}
.section-recommend{
  text-align: left;
    margin-left: 10px;
}
.salary{
  border-color: #e6e7e8;
  border: 1px solid #e6e7e8;
  background-color: #caeefa;
  width: 97%;
  padding-top: 15px;
}
.margin-top0{
  margin-top: 0;
}
.margin-left10px{
  margin-left: 10px;
}
.margin-bottom15px{
  margin-bottom: 15px;
}
.margin-left67px{
  margin-left: 67px;
}
.margin-left70px{
  margin-left: 70px;
}
.line{
  border-top: 1px solid #e6e7e8;
  width: 330px;
}
.bg-white{
  margin-top: 10px;
  background-color: white;
  width: 355px;
  height: 670px;
  border-radius: 5px;
}
.button-color2{
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  background-color: #057dbb;
  border-color: #007dbc;
  border-radius: 30px;
  width: 288px;
  height: 32px;
  border: none;
}
.paragraph{
  font-size: 15px !important;
  font-weight: 600;
  margin-left: 22px;
}
.button-contact{
  display: flex;
  justify-content: center;
}
.contact-section{ 
  margin: 0 13px;
  border-color: #e6e7e8;
  border: 1px solid #77d5f3;
  background-color: #caeefa;
  padding-top: 15px;
  padding-bottom: 15px;
  border-radius: 5px;
}
.button-blue {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font-family: inherit;
  font-size: 12px !important;
  color: #007dbc;
  cursor: pointer;
  font-weight: 600;
  margin-left: -9px;
}
.sa-share-position{
  text-align: left;
  margin-left: 32px;
  margin-top: 10px;
}
.sa-share-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  color: #007dbc;
  cursor: pointer;
  margin-left: -9px;
}
.sa-share-height{
  height: 15px;
  position: relative;
  top: 2px;
}
.display-inline{
  display: inline-block;
}
.filter-blur{
  filter: blur(2px);
}
.text-size14px{
  font-size: 14px;
}
.font-weight600{
  font-weight: 600;
}

.select-option{
  width:100.5px;
  height:20px;
  background:#f6f6f9;
  border:1px solid;
  border-color:#e6e7e8;
  font-size: 10px;
}

.flex-basis-60{
  flex-basis: 66%;
}
.hide{
  display: none;
}
.sa-logo{
  width: 90px !important;
    margin-left: 10px;
}
.icon_floatingLogo{
  position: fixed;
  top: 50px;
  right: 20px;
  z-index: 10010;
}
.icon_sa-expandArrow{
  position: fixed;
  top: 400px;
  right: 0;
  z-index: 10010;
  background-color: white;
}
.blur-text{
  position: relative;
    bottom: 7px;
}
.sa-expandArrow-border{
  border: 1px solid #e6e7e8;
  padding: 12px 10px 12px 20px;
  box-shadow: 0 1px 6px rgb(0 0 0 / 10%);
}
.recommend-salary{
  background-color: white;
    width: 355px;
    border-radius: 5px;
    border: 1px solid #e6e7e8;
    height: 80%;
}
.text-align-left{
  text-align: left;
}
.margin-left28px{
  margin-left: 28px;
}
.margin-left38px{
  margin-left: 38px;
}
.footer {
  position:absolute;
    color:#fff;
    bottom:0;
    width:100%;
    height:30px;
    line-height:30px;
    text-align:center;
    background-color: #f8f8f9 !important;
  /* align-items: center;
  justify-content: flex-start;
  display: flex;
  height: 30px;
  background-color: #f8f8f9 !important;
  z-index: 9999;
  clear: both; */
}

.footer p{
  text-align: left;
  font-family:Source Sans Pro;
color:#767676;
font-size:12px;
line-height:normal;
margin-left: 20px;
    margin-top: 10px;
}
.icon-triangle-right{
  display: none;
}
.icon-triangle-left,
.icon-triangle-right{
  width: 20px;
    height: 8px;
}
.icon-triangle-down{
  width: 20px;
  height: 6px;
}
.collapsed .sa-scope-btn .icon-triangle-down,
.collapsed .sa-job-btn .icon-triangle-down{
  display: none;
}
.collapsed .sa-scope-btn .icon-triangle-right,
.collapsed .sa-job-btn .icon-triangle-right{
  display: block;
}
.collapsed .icon-triangle-right{
  display: block;
  }
  .collapsed .icon-triangle-down{
    display: none;
    }

    .margin-bottom15{
      margin-bottom: 15px !important;
    }
    .display-flex{
display: flex;
    }
    .margin-right10{
      margin-right: 10px;
    }
    .flex-align-item-center{
      align-items: center;
    }

    .sa-content{
      margin-top: 10px;
    border-color: #e6e7e8;
    border: 1px solid #e6e7e8;
    background-color: white;
    width: 355px;
    padding-bottom: 50px;
    border-radius: 5px;
    }
/* .sa-job-info,.sa-salary-info,.sa-nodata-div{
  background:#ffffff;
} */