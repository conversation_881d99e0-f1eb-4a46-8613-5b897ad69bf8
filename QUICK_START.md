# Quick Start Guide - Job Salary Chrome Plugin

## 🚀 Fastest Way to Get Started (Recommended)

### Prerequisites
- Node.js 14+ installed ([Download here](https://nodejs.org/))
- Chrome browser
- Basic familiarity with command line

### Step 1: Install Dependencies
```bash
# Navigate to your project directory
cd "d:\SalaryRoot\JobSalaryChromePlugin\JobSalaryChromePlugin"

# Install Node.js dependencies
npm install
```

### Step 2: Start the Local Server
```bash
# Start the development server
npm start
```

You should see:
```
🚀 Job Salary Chrome Plugin Server is running!
📍 Server URL: http://localhost:8889
🌐 Main Interface: http://localhost:8889/JobSalary/index.html
```

### Step 3: Install Chrome Extension

1. **Open Chrome Extensions Page:**
   - Go to `chrome://extensions/`
   - Toggle "Developer mode" ON (top right)

2. **Load the Extension:**
   - Click "Load unpacked"
   - Select the `ChromePlugin` folder from your project
   - Extension should appear with name "Real-time Job Posting Salary Data"

### Step 4: Test the System

1. **Test the Web Interface:**
   - Open: http://localhost:8889/JobSalary/index.html
   - You should see the salary plugin interface

2. **Test on a Job Site:**
   - Go to LinkedIn Jobs: https://www.linkedin.com/jobs/
   - Search for any job and open a job posting
   - Look for the floating salary icon on the right side of the page
   - Click it to open the salary analysis panel

## 🔧 How the System Works

### Configuration System (`jdspider.json`)

The system uses CSS selectors to extract job information from different websites:

```json
{
    "url": "https://www.linkedin.com/jobs/",
    "urltype": "1",
    "iframe": "",
    "title": "h2[class^=\"top-card-layout__title\"]",
    "desc": "div[class^=\"description__text\"]"
}
```

**Field Explanations:**
- `url`: Website URL pattern to match
- `urltype`: "1" = exact match, "2" = contains pattern
- `title`: CSS selector for job title
- `desc`: CSS selector for job description

### Supported Job Sites (Pre-configured)

✅ **Currently Supported:**
- LinkedIn Jobs
- Indeed
- Glassdoor  
- ZipRecruiter
- Monster
- Dice
- CareerBuilder
- Simply Hired
- And 15+ more job boards

### Workflow

1. **Navigate** to a supported job site
2. **Open** a job posting page
3. **Click** the floating salary icon or extension button
4. **Review** extracted job title and description
5. **Click** "Get Salary Range For This Job" for instant analysis
6. **View** salary recommendations and market data

## 🛠️ Adding New Job Sites

To add support for a new job board:

### Step 1: Analyze the Website
1. Open the job site and go to a job posting
2. Open Chrome DevTools (F12)
3. Find CSS selectors for:
   - Job title element
   - Job description element

### Step 2: Add Configuration
Edit `JobSalary/jdspider.json`:

```json
{
    "url": "https://newjobsite.com/jobs/",
    "urltype": "1",
    "iframe": "",
    "title": ".job-title-class",
    "desc": ".job-description-class"
}
```

### Step 3: Update Extension Permissions
Add the domain to `ChromePlugin/manifest.json`:

```json
"host_permissions": [
    "https://newjobsite.com/*"
]
```

### Step 4: Test
1. Restart the server: `npm start`
2. Reload the Chrome extension
3. Test on the new job site

## 🔍 Testing Selectors

Use Chrome DevTools Console to test selectors:

```javascript
// Test if selector finds the job title
document.querySelector("h2[class^='top-card-layout__title']")

// Test if selector finds job description  
document.querySelector("div[class^='description__text']")
```

## 🐛 Troubleshooting

### Extension Not Loading
- Check Chrome Extensions page for errors
- Verify all files are in `ChromePlugin` folder
- Check browser console for JavaScript errors

### Server Not Starting
```bash
# Check if port 8889 is in use
netstat -an | findstr :8889

# Try a different port
PORT=8890 npm start
```

### Job Data Not Extracting
- Website may have changed HTML structure
- Update CSS selectors in `jdspider.json`
- Check browser console for errors
- Verify the job site is in the supported list

### CORS Errors
- Ensure server is running on correct port
- Check that `jobSalaryHost` in `contentScript.js` matches server URL

## 📝 Development Tips

### Debugging the Extension
1. Right-click extension icon → "Inspect popup"
2. Check Chrome Extensions page for background script errors
3. Use `console.log()` in content scripts

### Testing API Responses
```bash
# Test health endpoint
curl http://localhost:8889/health

# Test job salary endpoint
curl "http://localhost:8889/JobSalary/GetJobSalary.aspx?JobTitle=Developer&JobDesc=Software%20Developer"
```

### Modifying the UI
- Edit `JobSalary/index.html` for interface changes
- Edit `JobSalary/styles.css` for styling
- Edit `JobSalary/main.js` for functionality

## 🔄 Next Steps

Once you have the basic system running:

1. **Test on Multiple Job Sites** - Verify extraction works correctly
2. **Customize the Interface** - Modify colors, layout, messaging
3. **Add New Job Boards** - Expand support to more websites
4. **Enhance Data Collection** - Add fields like company, location, salary
5. **Implement Data Storage** - Save extracted data to database
6. **Add Analytics** - Track usage and extraction success rates

## 📞 Need Help?

If you encounter issues:
1. Check the console logs in both browser and server
2. Verify all configuration files are correct
3. Test individual components (server, extension, job sites)
4. Review the detailed setup instructions in `SETUP_INSTRUCTIONS.md`

Happy job data collecting! 🎯
