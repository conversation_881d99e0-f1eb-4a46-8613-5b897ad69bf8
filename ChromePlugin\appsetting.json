{"jobSalaryHost": "http://localhost:8889", "ApiSetting": [{"Enable": false, "ClientID": "05618CBA-5330-4F1B-9726-F89D62FB54D0", "ClientSecret": "BED43FB0-B9E5-4780-9CBE-557E1A61AC77", "TokenUrl": "https://salarystaging40.salarynet.local/token", "GetSalaryUrl": "https://salarystaging40.salarynet.local/DaaS/jobpricing"}, {"Enable": true, "ClientID": "A029DDB8-5201-47E2-8949-D0E5CDF27B95", "ClientSecret": "469F752F-9977-44E2-8318-09D5BB9CFF62", "TokenUrl": "http://daasjobmatchapi.salary.com/token", "GetSalaryUrl": "http://daasjobmatchapi.salary.com/token/DaaS/jobpricing"}]}